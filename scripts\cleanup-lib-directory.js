#!/usr/bin/env node

/**
 * Cleanup Misplaced lib Directory
 * Removes TypeScript build cache files from wrong location
 */

const fs = require('fs');
const path = require('path');

function cleanupLibDirectory() {
  console.log('🧹 Cleaning up misplaced lib/ directory...\n');

  const libDir = 'lib';
  const tsBuildInfoFile = path.join(libDir, 'tsconfig.tsbuildinfo');

  // Check if lib directory exists
  if (!fs.existsSync(libDir)) {
    console.log('✅ No lib/ directory found in root - already clean!');
    return;
  }

  console.log('📁 Found lib/ directory in root (should not exist)');

  // List contents
  try {
    const contents = fs.readdirSync(libDir);
    console.log('📋 Contents of lib/ directory:');
    contents.forEach(file => {
      const filePath = path.join(libDir, file);
      const stats = fs.statSync(filePath);
      const type = stats.isDirectory() ? 'DIR' : 'FILE';
      const size = stats.isFile() ? `(${stats.size} bytes)` : '';
      console.log(`   • ${file} [${type}] ${size}`);
    });
    console.log('');

    // Analyze each file
    console.log('🔍 ANALYSIS:');
    contents.forEach(file => {
      const filePath = path.join(libDir, file);
      
      if (file === 'tsconfig.tsbuildinfo') {
        console.log(`   • ${file} - TypeScript build cache (SAFE TO DELETE)`);
        console.log('     Reason: Build cache files are regenerated automatically');
      } else if (file.endsWith('.tsbuildinfo')) {
        console.log(`   • ${file} - TypeScript build cache (SAFE TO DELETE)`);
      } else if (file.endsWith('.js') || file.endsWith('.js.map')) {
        console.log(`   • ${file} - Compiled JavaScript (SAFE TO DELETE)`);
        console.log('     Reason: Should be in functions/lib/ not root lib/');
      } else {
        console.log(`   • ${file} - Unknown file (REVIEW NEEDED)`);
      }
    });
    console.log('');

    // Safe deletion
    console.log('🗑️  SAFE DELETION:');
    let deletedCount = 0;
    let skippedCount = 0;

    contents.forEach(file => {
      const filePath = path.join(libDir, file);
      
      // Only delete known safe files
      if (file.endsWith('.tsbuildinfo') || 
          file.endsWith('.js') || 
          file.endsWith('.js.map') ||
          file.endsWith('.d.ts')) {
        try {
          if (fs.statSync(filePath).isFile()) {
            fs.unlinkSync(filePath);
            console.log(`   ✅ Deleted: ${file}`);
            deletedCount++;
          }
        } catch (error) {
          console.log(`   ❌ Failed to delete ${file}: ${error.message}`);
        }
      } else {
        console.log(`   ⚠️  Skipped: ${file} (unknown file type)`);
        skippedCount++;
      }
    });

    // Remove directory if empty
    try {
      const remainingContents = fs.readdirSync(libDir);
      if (remainingContents.length === 0) {
        fs.rmdirSync(libDir);
        console.log('   ✅ Removed empty lib/ directory');
      } else {
        console.log(`   ⚠️  lib/ directory not empty, contains: ${remainingContents.join(', ')}`);
      }
    } catch (error) {
      console.log(`   ⚠️  Could not remove lib/ directory: ${error.message}`);
    }

    console.log('\n📊 CLEANUP SUMMARY:');
    console.log(`   ✅ Files deleted: ${deletedCount}`);
    console.log(`   ⚠️  Files skipped: ${skippedCount}`);

  } catch (error) {
    console.log(`❌ Error reading lib/ directory: ${error.message}`);
    return;
  }

  console.log('\n🎯 ROOT CAUSE & PREVENTION:');
  console.log('');
  console.log('📋 Why this happened:');
  console.log('   • TypeScript build cache created in wrong location');
  console.log('   • Should be in project root or functions/lib/');
  console.log('   • Likely from IDE or build tool misconfiguration');
  console.log('');
  
  console.log('🛡️  Prevention measures:');
  console.log('   • .gitignore already includes *.tsbuildinfo');
  console.log('   • Main tsconfig.json has "noEmit": true');
  console.log('   • Functions tsconfig.json outputs to functions/lib/');
  console.log('   • This cleanup prevents future confusion');
  console.log('');

  console.log('✅ CORRECT STRUCTURE:');
  console.log('   tap2go/');
  console.log('   ├── src/                    # Application source');
  console.log('   ├── functions/');
  console.log('   │   ├── src/               # Functions source');
  console.log('   │   └── lib/               # Functions build output (CORRECT)');
  console.log('   ├── tsconfig.json          # Main TypeScript config');
  console.log('   └── *.tsbuildinfo          # Build cache in root (CORRECT)');
  console.log('');
  console.log('❌ INCORRECT (what we removed):');
  console.log('   tap2go/');
  console.log('   └── lib/                   # Should NOT exist in root');
  console.log('       └── tsconfig.tsbuildinfo');
  console.log('');

  console.log('🎉 ROOT DIRECTORY CLEANUP COMPLETE!');
  console.log('');
  console.log('✨ BENEFITS ACHIEVED:');
  console.log('   • Removed misplaced build files');
  console.log('   • Cleaner root directory structure');
  console.log('   • Prevented developer confusion');
  console.log('   • Aligned with TypeScript best practices');
  console.log('');
  
  console.log('📋 NEXT STEPS:');
  console.log('   1. Verify TypeScript compilation still works');
  console.log('   2. Check that functions build properly');
  console.log('   3. Ensure IDE TypeScript service works correctly');
  console.log('   4. Build cache will regenerate automatically when needed');
}

// Run the cleanup
cleanupLibDirectory();
