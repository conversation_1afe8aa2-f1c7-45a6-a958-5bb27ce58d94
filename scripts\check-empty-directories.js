#!/usr/bin/env node

/**
 * Check for Empty Directories in src/app
 * Systematically checks every directory to identify empty ones
 */

const fs = require('fs');
const path = require('path');

function isDirectoryEmpty(dirPath) {
  try {
    const files = fs.readdirSync(dirPath);
    // Filter out hidden files and check if any real content exists
    const realFiles = files.filter(file => !file.startsWith('.'));
    
    if (realFiles.length === 0) {
      return true; // Completely empty
    }
    
    // Check if all remaining items are empty directories
    for (const file of realFiles) {
      const fullPath = path.join(dirPath, file);
      const stat = fs.statSync(fullPath);
      
      if (stat.isFile()) {
        return false; // Has files, not empty
      } else if (stat.isDirectory()) {
        if (!isDirectoryEmpty(fullPath)) {
          return false; // Has non-empty subdirectory
        }
      }
    }
    
    return true; // Only empty subdirectories
  } catch (error) {
    console.log(`❌ Error checking ${dirPath}: ${error.message}`);
    return false;
  }
}

function getAllDirectories(dirPath, basePath = '') {
  const directories = [];
  
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      if (item.startsWith('.')) continue; // Skip hidden files
      
      const fullPath = path.join(dirPath, item);
      const relativePath = path.join(basePath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        directories.push({
          path: fullPath,
          relativePath: relativePath,
          isEmpty: isDirectoryEmpty(fullPath)
        });
        
        // Recursively get subdirectories
        const subDirs = getAllDirectories(fullPath, relativePath);
        directories.push(...subDirs);
      }
    }
  } catch (error) {
    console.log(`❌ Error reading directory ${dirPath}: ${error.message}`);
  }
  
  return directories;
}

function checkEmptyDirectories() {
  console.log('🔍 Checking for empty directories in src/app...\n');
  
  const appPath = 'src/app';
  
  if (!fs.existsSync(appPath)) {
    console.log('❌ src/app directory not found!');
    return;
  }
  
  const allDirectories = getAllDirectories(appPath);
  const emptyDirectories = allDirectories.filter(dir => dir.isEmpty);
  const nonEmptyDirectories = allDirectories.filter(dir => !dir.isEmpty);
  
  console.log('📊 DIRECTORY ANALYSIS RESULTS:\n');
  
  // Show empty directories
  if (emptyDirectories.length > 0) {
    console.log('🗑️  EMPTY DIRECTORIES (can be removed):');
    emptyDirectories.forEach(dir => {
      console.log(`   ❌ ${dir.relativePath}`);
    });
    console.log(`\n   Total empty directories: ${emptyDirectories.length}\n`);
  } else {
    console.log('✅ No empty directories found!\n');
  }
  
  // Show non-empty directories
  console.log('📁 NON-EMPTY DIRECTORIES (contain files or content):');
  nonEmptyDirectories.forEach(dir => {
    console.log(`   ✅ ${dir.relativePath}`);
  });
  console.log(`\n   Total directories with content: ${nonEmptyDirectories.length}\n`);
  
  // Summary
  console.log('📋 SUMMARY:');
  console.log(`   Total directories checked: ${allDirectories.length}`);
  console.log(`   Empty directories: ${emptyDirectories.length}`);
  console.log(`   Directories with content: ${nonEmptyDirectories.length}`);
  
  if (emptyDirectories.length > 0) {
    console.log('\n🧹 CLEANUP RECOMMENDATION:');
    console.log('   The empty directories listed above can be safely removed.');
    console.log('   They are likely leftover from development or incomplete features.');
    console.log('\n💡 To remove them, run:');
    console.log('   node scripts/remove-empty-directories.js');
  } else {
    console.log('\n🎉 Your src/app directory is clean - no empty directories found!');
  }
}

// Run the check
checkEmptyDirectories();
