#!/usr/bin/env node

/**
 * Analyze Firebase Files Organization Safety
 * Determines if Firebase configuration files can be safely moved to a firebase/ directory
 */

const fs = require('fs');
const path = require('path');

function analyzeFirebaseFiles() {
  console.log('🔍 Analyzing Firebase files organization safety...\n');

  // Firebase files in root directory
  const firebaseFiles = [
    'firebase.json',
    'firestore.rules', 
    'firestore.dev.rules',
    'firestore.indexes.json',
    'functions/'
  ];

  console.log('📁 FIREBASE FILES IN ROOT DIRECTORY:\n');
  
  firebaseFiles.forEach(file => {
    const exists = fs.existsSync(file);
    console.log(`   ${exists ? '✅' : '❌'} ${file} ${exists ? '(exists)' : '(not found)'}`);
  });

  console.log('\n🔍 DETAILED ANALYSIS:\n');

  // Analyze firebase.json
  console.log('📄 firebase.json:');
  console.log('   Purpose: Firebase CLI configuration file');
  console.log('   Critical: YES - Firebase CLI requires this in project root');
  console.log('   Can Move: ❌ NO - Firebase CLI looks for this file in project root');
  console.log('   References: firestore.rules, firestore.indexes.json, functions/');
  console.log('   Impact: Moving would break Firebase CLI commands');
  console.log('');

  // Analyze firestore.rules
  console.log('📄 firestore.rules:');
  console.log('   Purpose: Firestore security rules');
  console.log('   Critical: YES - Database security depends on this');
  console.log('   Can Move: ⚠️  CONDITIONAL - Only if firebase.json is updated');
  console.log('   Referenced by: firebase.json ("rules": "firestore.rules")');
  console.log('   Impact: Can move if firebase.json path is updated');
  console.log('');

  // Analyze firestore.dev.rules
  console.log('📄 firestore.dev.rules:');
  console.log('   Purpose: Development Firestore security rules');
  console.log('   Critical: MEDIUM - Used for development environment');
  console.log('   Can Move: ✅ YES - Not referenced by firebase.json');
  console.log('   Referenced by: Manual deployment only');
  console.log('   Impact: Safe to move, update deployment scripts');
  console.log('');

  // Analyze firestore.indexes.json
  console.log('📄 firestore.indexes.json:');
  console.log('   Purpose: Firestore database indexes');
  console.log('   Critical: YES - Database performance depends on this');
  console.log('   Can Move: ⚠️  CONDITIONAL - Only if firebase.json is updated');
  console.log('   Referenced by: firebase.json ("indexes": "firestore.indexes.json")');
  console.log('   Impact: Can move if firebase.json path is updated');
  console.log('');

  // Analyze functions directory
  console.log('📁 functions/:');
  console.log('   Purpose: Firebase Cloud Functions source code');
  console.log('   Critical: YES - PayMongo webhooks and FCM notifications');
  console.log('   Can Move: ⚠️  CONDITIONAL - Only if firebase.json is updated');
  console.log('   Referenced by: firebase.json ("source": "functions")');
  console.log('   Impact: Can move if firebase.json path is updated');
  console.log('');

  // Check package.json scripts
  console.log('🔧 PACKAGE.JSON FIREBASE SCRIPTS:\n');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const firebaseScripts = Object.entries(packageJson.scripts || {})
      .filter(([key, value]) => value.includes('firebase'));
    
    if (firebaseScripts.length > 0) {
      firebaseScripts.forEach(([script, command]) => {
        console.log(`   • ${script}: ${command}`);
      });
    } else {
      console.log('   No Firebase scripts found in package.json');
    }
  } catch (error) {
    console.log('   ❌ Could not read package.json');
  }

  console.log('\n📊 ORGANIZATION RECOMMENDATIONS:\n');

  console.log('🚫 CANNOT MOVE (Firebase CLI Requirements):');
  console.log('   • firebase.json - MUST stay in root (Firebase CLI requirement)');
  console.log('');

  console.log('⚠️  CAN MOVE WITH UPDATES (Update firebase.json paths):');
  console.log('   • firestore.rules → firebase/firestore.rules');
  console.log('   • firestore.indexes.json → firebase/firestore.indexes.json');
  console.log('   • functions/ → firebase/functions/');
  console.log('');

  console.log('✅ SAFE TO MOVE (No references):');
  console.log('   • firestore.dev.rules → firebase/firestore.dev.rules');
  console.log('');

  console.log('🎯 RECOMMENDED ORGANIZATION STRATEGY:\n');

  console.log('Option 1: MINIMAL ORGANIZATION (Safest)');
  console.log('   • Keep firebase.json in root (required)');
  console.log('   • Move only firestore.dev.rules to firebase/');
  console.log('   • Keep other files in root for Firebase CLI compatibility');
  console.log('');

  console.log('Option 2: FULL ORGANIZATION (Requires Updates)');
  console.log('   • Keep firebase.json in root');
  console.log('   • Move all other Firebase files to firebase/');
  console.log('   • Update firebase.json paths:');
  console.log('     - "rules": "firebase/firestore.rules"');
  console.log('     - "indexes": "firebase/firestore.indexes.json"');
  console.log('     - "source": "firebase/functions"');
  console.log('   • Update package.json scripts');
  console.log('   • Update documentation');
  console.log('');

  console.log('💡 PROFESSIONAL RECOMMENDATION:\n');
  console.log('   For a production food delivery platform like Tap2Go:');
  console.log('   • KEEP current structure - Firebase files in root');
  console.log('   • This follows Firebase best practices');
  console.log('   • Ensures compatibility with Firebase CLI');
  console.log('   • Prevents deployment issues');
  console.log('   • Standard practice in Firebase projects');
  console.log('');

  console.log('🔍 WHY KEEP FIREBASE FILES IN ROOT:\n');
  console.log('   1. Firebase CLI expects firebase.json in project root');
  console.log('   2. Standard Firebase project structure');
  console.log('   3. Easier CI/CD pipeline configuration');
  console.log('   4. Better compatibility with Firebase tooling');
  console.log('   5. Follows Google\'s recommended project structure');
  console.log('   6. Reduces risk of deployment failures');
  console.log('');

  console.log('✨ CONCLUSION:\n');
  console.log('   Your Firebase files are correctly organized in the root directory.');
  console.log('   This is the STANDARD and RECOMMENDED structure for Firebase projects.');
  console.log('   Moving them would add complexity without significant benefits.');
  console.log('   Focus on other areas of codebase organization instead.');
}

// Run the analysis
analyzeFirebaseFiles();
