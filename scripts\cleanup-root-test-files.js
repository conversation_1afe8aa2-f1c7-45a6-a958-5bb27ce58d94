#!/usr/bin/env node

/**
 * Cleanup Root Test Files
 * Moves important test files to organized structure and removes redundant ones
 */

const fs = require('fs');
const path = require('path');

// Define what to do with each test file
const testFileActions = {
  // MOVE to organized structure (important functionality)
  move: [
    {
      from: 'test-email-simple.js',
      to: 'src/tests/scripts/email/test-email-simple.js',
      reason: 'Email testing functionality - move to email tests'
    },
    {
      from: 'verify-restaurant-images.js', 
      to: 'src/tests/scripts/database/verify-restaurant-images.js',
      reason: 'Database verification script - move to database tests'
    },
    {
      from: 'test-bonsai.js',
      to: 'src/tests/scripts/integrations/test-bonsai.js', 
      reason: 'OpenSearch integration test - move to integrations'
    },
    {
      from: 'public/test-posts-api.html',
      to: 'src/tests/scripts/integrations/test-posts-api.html',
      reason: 'API testing interface - move to integrations'
    }
  ],
  
  // DELETE (redundant or obsolete)
  delete: [
    {
      file: 'test-hard-delete.js',
      reason: 'Likely obsolete - hard delete functionality testing'
    },
    {
      file: 'test-production-api.html',
      reason: 'Redundant - similar functionality in organized tests'
    },
    {
      file: 'test-upload-refresh.md',
      reason: 'Documentation file - not a test script'
    },
    {
      file: 'TESTING_ORGANIZATION_COMPLETE.md',
      reason: 'Temporary documentation - organization is complete'
    }
  ]
};

function ensureDirectoryExists(filePath) {
  const dir = path.dirname(filePath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`📁 Created directory: ${dir}`);
  }
}

function moveFile(from, to, reason) {
  try {
    if (!fs.existsSync(from)) {
      console.log(`⚠️  File not found: ${from}`);
      return false;
    }
    
    ensureDirectoryExists(to);
    
    // Copy file content
    const content = fs.readFileSync(from);
    fs.writeFileSync(to, content);
    
    // Remove original
    fs.unlinkSync(from);
    
    console.log(`✅ Moved: ${from} → ${to}`);
    console.log(`   Reason: ${reason}`);
    return true;
  } catch (error) {
    console.log(`❌ Failed to move ${from}: ${error.message}`);
    return false;
  }
}

function deleteFile(file, reason) {
  try {
    if (!fs.existsSync(file)) {
      console.log(`⚠️  File not found: ${file}`);
      return false;
    }
    
    fs.unlinkSync(file);
    console.log(`🗑️  Deleted: ${file}`);
    console.log(`   Reason: ${reason}`);
    return true;
  } catch (error) {
    console.log(`❌ Failed to delete ${file}: ${error.message}`);
    return false;
  }
}

function cleanupRootTestFiles() {
  console.log('🧹 Cleaning up root directory test files...\n');
  
  let movedCount = 0;
  let deletedCount = 0;
  let failedCount = 0;
  
  // Move important test files
  console.log('📦 MOVING IMPORTANT TEST FILES:\n');
  testFileActions.move.forEach(action => {
    if (moveFile(action.from, action.to, action.reason)) {
      movedCount++;
    } else {
      failedCount++;
    }
    console.log('');
  });
  
  // Delete redundant/obsolete files
  console.log('🗑️  REMOVING REDUNDANT/OBSOLETE FILES:\n');
  testFileActions.delete.forEach(action => {
    if (deleteFile(action.file, action.reason)) {
      deletedCount++;
    } else {
      failedCount++;
    }
    console.log('');
  });
  
  // Summary
  console.log('📊 CLEANUP SUMMARY:');
  console.log(`✅ Files moved to organized structure: ${movedCount}`);
  console.log(`🗑️  Files deleted (redundant/obsolete): ${deletedCount}`);
  console.log(`❌ Failed operations: ${failedCount}`);
  
  if (movedCount > 0 || deletedCount > 0) {
    console.log('\n🎉 Root directory cleanup completed!');
    console.log('📁 Important test files are now in organized structure:');
    console.log('   • Email tests: src/tests/scripts/email/');
    console.log('   • Database tests: src/tests/scripts/database/');
    console.log('   • Integration tests: src/tests/scripts/integrations/');
    
    console.log('\n📋 NEXT STEPS:');
    console.log('1. Update package.json scripts to reference new locations');
    console.log('2. Update any documentation that references old file paths');
    console.log('3. Test the moved scripts to ensure they still work');
    console.log('4. Run: npm run test:dashboard to access organized tests');
  }
  
  if (failedCount === 0) {
    console.log('\n✨ Your root directory is now clean and organized!');
  }
}

// Run the cleanup
cleanupRootTestFiles();
