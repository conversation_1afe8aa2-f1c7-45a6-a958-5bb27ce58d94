#!/usr/bin/env node

/**
 * Check for Test Files in Root Directory
 * Identifies test files that should be moved to organized structure
 */

const fs = require('fs');
const path = require('path');

function checkRootTestFiles() {
  console.log('🔍 Checking for test files in root directory...\n');
  
  const rootFiles = fs.readdirSync('.');
  
  // Identify test-related files
  const testFiles = [];
  const testPatterns = [
    /^test-/i,           // Files starting with "test-"
    /test\.js$/i,        // Files ending with "test.js"
    /\.test\./i,         // Files containing ".test."
    /test.*\.html$/i,    // Test HTML files
    /test.*\.md$/i,      // Test markdown files
    /verify.*\.js$/i,    // Verification scripts
  ];
  
  rootFiles.forEach(file => {
    // Skip directories and hidden files
    if (file.startsWith('.') || fs.statSync(file).isDirectory()) {
      return;
    }
    
    // Check if file matches test patterns
    const isTestFile = testPatterns.some(pattern => pattern.test(file));
    
    if (isTestFile) {
      const stats = fs.statSync(file);
      testFiles.push({
        name: file,
        size: stats.size,
        modified: stats.mtime.toISOString().split('T')[0]
      });
    }
  });
  
  // Also check public directory for test files
  const publicTestFiles = [];
  if (fs.existsSync('public')) {
    const publicFiles = fs.readdirSync('public');
    publicFiles.forEach(file => {
      if (testPatterns.some(pattern => pattern.test(file))) {
        const stats = fs.statSync(path.join('public', file));
        publicTestFiles.push({
          name: `public/${file}`,
          size: stats.size,
          modified: stats.mtime.toISOString().split('T')[0]
        });
      }
    });
  }
  
  console.log('📊 ROOT DIRECTORY TEST FILES ANALYSIS:\n');
  
  if (testFiles.length > 0) {
    console.log('🗑️  TEST FILES IN ROOT (should be moved/removed):');
    testFiles.forEach(file => {
      console.log(`   ❌ ${file.name} (${(file.size / 1024).toFixed(1)}KB, modified: ${file.modified})`);
    });
    console.log(`\n   Total root test files: ${testFiles.length}\n`);
  }
  
  if (publicTestFiles.length > 0) {
    console.log('🗑️  TEST FILES IN PUBLIC (should be moved/removed):');
    publicTestFiles.forEach(file => {
      console.log(`   ❌ ${file.name} (${(file.size / 1024).toFixed(1)}KB, modified: ${file.modified})`);
    });
    console.log(`\n   Total public test files: ${publicTestFiles.length}\n`);
  }
  
  const totalTestFiles = testFiles.length + publicTestFiles.length;
  
  if (totalTestFiles === 0) {
    console.log('✅ No test files found in root directory!\n');
    console.log('🎉 Your root directory is clean.');
  } else {
    console.log('📋 CLEANUP RECOMMENDATIONS:\n');
    
    console.log('🔄 MOVE TO ORGANIZED STRUCTURE:');
    testFiles.concat(publicTestFiles).forEach(file => {
      if (file.name.includes('email')) {
        console.log(`   • ${file.name} → src/tests/scripts/email/`);
      } else if (file.name.includes('production') || file.name.includes('api')) {
        console.log(`   • ${file.name} → src/tests/scripts/integrations/`);
      } else if (file.name.includes('verify')) {
        console.log(`   • ${file.name} → src/tests/scripts/database/`);
      } else {
        console.log(`   • ${file.name} → src/tests/scripts/utilities/`);
      }
    });
    
    console.log('\n🗑️  OR SAFE TO DELETE (if redundant):');
    testFiles.concat(publicTestFiles).forEach(file => {
      console.log(`   • ${file.name} (if functionality exists in organized tests)`);
    });
    
    console.log('\n💡 NEXT STEPS:');
    console.log('1. Review each file to determine if it\'s still needed');
    console.log('2. Move important test files to src/tests/scripts/');
    console.log('3. Delete redundant or obsolete test files');
    console.log('4. Run: node scripts/cleanup-root-test-files.js');
  }
  
  console.log('\n📊 SUMMARY:');
  console.log(`   Root test files: ${testFiles.length}`);
  console.log(`   Public test files: ${publicTestFiles.length}`);
  console.log(`   Total test files to clean: ${totalTestFiles}`);
}

// Run the check
checkRootTestFiles();
