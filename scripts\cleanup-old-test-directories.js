#!/usr/bin/env node

/**
 * Cleanup Old Test Directories
 * Removes empty test directories from src/app after migration to src/tests
 */

const fs = require('fs');
const path = require('path');

const testDirectoriesToRemove = [
  'src/app/test',
  'src/app/test-admin',
  'src/app/test-admin-login', 
  'src/app/test-all-notifications',
  'src/app/test-api',
  'src/app/test-auth',
  'src/app/test-auth-flow',
  'src/app/test-chat',
  'src/app/test-complete-flow',
  'src/app/test-customer',
  'src/app/test-customers',
  'src/app/test-notifications',
  'src/app/test-restaurant',
  'src/app/test-simple',
  'src/app/test-vendor',
  'src/app/test-webhook',
  // Empty API test directories
  'src/app/api/test-connection',
  'src/app/api/test-db', 
  'src/app/api/test-post-creation'
];

function removeDirectoryRecursive(dirPath) {
  if (fs.existsSync(dirPath)) {
    try {
      // Remove directory and all contents
      fs.rmSync(dirPath, { recursive: true, force: true });
      console.log(`✅ Removed: ${dirPath}`);
      return true;
    } catch (error) {
      console.log(`❌ Failed to remove ${dirPath}: ${error.message}`);
      return false;
    }
  } else {
    console.log(`⚠️  Directory not found: ${dirPath}`);
    return false;
  }
}

function cleanupOldTestDirectories() {
  console.log('🧹 Cleaning up old test directories...\n');
  
  let removedCount = 0;
  let failedCount = 0;
  
  testDirectoriesToRemove.forEach(dirPath => {
    if (removeDirectoryRecursive(dirPath)) {
      removedCount++;
    } else {
      failedCount++;
    }
  });
  
  console.log('\n📊 Cleanup Summary:');
  console.log(`✅ Successfully removed: ${removedCount} directories`);
  console.log(`❌ Failed to remove: ${failedCount} directories`);
  
  if (removedCount > 0) {
    console.log('\n🎉 Old test directories have been cleaned up!');
    console.log('📁 All tests are now organized in: src/tests/');
    console.log('🚀 Access your organized tests at: http://localhost:3000/tests/dashboard');
  }
  
  console.log('\n📋 Next steps:');
  console.log('1. Add ENABLE_TEST_ROUTES=true to .env.local');
  console.log('2. Run: npm run test:dashboard');
  console.log('3. Explore the new organized structure in src/tests/');
}

// Run the cleanup
cleanupOldTestDirectories();
